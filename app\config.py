"""
应用配置管理
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用设置"""
    
    # Poe API 配置（现在通过请求参数传递）
    poe_api_key: Optional[str] = Field(None, description="Poe API 密钥（可选，现在通过请求参数传递）")
    
    # 代理配置
    use_proxy: bool = Field(False, description="是否使用代理")
    proxy_host: str = Field("127.0.0.1", description="代理主机")
    proxy_port: int = Field(10300, description="代理端口")
    
    # 服务器配置
    server_host: str = Field("0.0.0.0", description="服务器主机")
    server_port: int = Field(8081, description="服务器端口")
    log_level: str = Field("info", description="日志级别")
    
    # 应用配置
    app_name: str = Field("Poe API 服务", description="应用名称")
    app_version: str = Field("1.0.0", description="应用版本")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    def setup_proxy(self) -> None:
        """设置代理环境变量"""
        if self.use_proxy:
            proxy_url = f"http://{self.proxy_host}:{self.proxy_port}"
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            print(f"✅ 代理已启用: {proxy_url}")
        else:
            # 清除代理设置
            os.environ.pop('HTTP_PROXY', None)
            os.environ.pop('HTTPS_PROXY', None)
            print("❌ 代理已禁用")

    @property
    def proxy_url(self) -> Optional[str]:
        """获取代理 URL"""
        if self.use_proxy:
            return f"http://{self.proxy_host}:{self.proxy_port}"
        return None


# 创建全局设置实例
settings = Settings()

# 自动设置代理
settings.setup_proxy()
