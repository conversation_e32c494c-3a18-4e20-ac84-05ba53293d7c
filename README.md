# Poe API FastAPI 服务

基于 FastAPI 和 fastapi_poe 构建的 Poe API Web 服务，提供标准的 REST API 接口来调用 Poe 上的各种 AI 模型。

## 功能特性

### 🚀 核心功能
- 🚀 基于 FastAPI 的高性能 Web 服务
- 🤖 支持调用 Poe 上的各种 AI 模型
- 🔧 支持代理配置
- 📝 完整的 API 文档
- 🏗️ 标准的项目结构
- ⚙️ 环境变量配置管理
- 🌐 完全开放的 CORS 配置（允许任何域名和端口）
- 🔄 使用现代 FastAPI lifespan 事件管理
- 🔥 开发模式热重载支持

### 🎯 高级功能
- 🎭 **system_prompt 角色设定** - 为AI设置角色和行为模式，支持各种专业场景
- 💬 **多轮会话支持** - 支持对话历史，AI能记住之前的对话内容
- 📁 **文件处理能力** - 支持文件上传分析和AI生成文件接收
- 🔄 **向后兼容** - 所有新功能都保持向后兼容，现有代码无需修改

## 项目结构

```
poe-connection/
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── main.py            # FastAPI 应用入口
│   ├── config.py          # 配置管理
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   └── chat.py        # 聊天相关模型
│   ├── routers/           # 路由模块
│   │   ├── __init__.py
│   │   └── chat.py        # 聊天路由
│   └── services/          # 服务层
│       ├── __init__.py
│       └── poe_service.py # Poe API 服务
├── requirements.txt       # 项目依赖
├── .env                   # 环境变量（不提交到git）
├── .env.example          # 环境变量模板
├── .gitignore            # Git 忽略文件
├── test_api.py           # API 测试脚本
└── README.md             # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，设置你的 Poe API Key：

```env
POE_API_KEY=your_poe_api_key_here
USE_PROXY=true
PROXY_HOST=127.0.0.1
PROXY_PORT=10300
SERVER_PORT=8081
```

### 3. 启动服务

#### 使用启动脚本（推荐）

**开发模式：**
```bash
python run.py --dev
```

**生产模式：**
```bash
python run.py --prod
```

**自定义配置：**
```bash
python run.py --host 0.0.0.0 --port 8081 --reload
```

**查看所有选项：**
```bash
python run.py --help
```

#### 其他启动方式

**直接使用模块：**
```bash
python -m app.main
```

**使用 uvicorn：**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8081
```

### 4. 访问服务

- API 文档: http://localhost:8081/docs
- 服务状态: http://localhost:8081/
- 健康检查: http://localhost:8081/health

## API 接口

### POST /chat

发送聊天消息到指定的 Poe 机器人

**请求体:**
```json
{
  "role": "user",
  "content": "你好，请介绍一下你自己",
  "bot_name": "GPT-3.5-Turbo",
  "api_key": "your_poe_api_key_here",
  "system_prompt": "你是一个友善的AI助手，请用简洁明了的方式回答问题。"
}
```

**参数说明:**
- `system_prompt` (可选): 系统提示词，用于设置AI的角色和行为模式

**system_prompt 使用示例:**
```json
{
  "role": "user",
  "content": "如何学习Python？",
  "bot_name": "GPT-3.5-Turbo",
  "api_key": "your_poe_api_key_here",
  "system_prompt": "你是一个专业的Python编程导师，请用简洁专业的语言回答问题。"
}
```

**响应:**
```json
{
  "success": true,
  "message": "请求成功",
  "full_response": "你好！我是ChatGPT，一个AI助手...",
  "response_length": 50,
  "bot_name": "GPT-3.5-Turbo"
}
```

### POST /chat/with-files

发送带文件的聊天消息到指定的 Poe 机器人

**请求体:**
```json
{
  "role": "user",
  "content": "请分析这些文件的内容",
  "bot_name": "GPT-3.5-Turbo",
  "api_key": "your_poe_api_key_here",
  "file_urls": [
    "https://example.com/document.pdf",
    "https://example.com/data.txt"
  ]
}
```

**响应:**
```json
{
  "success": true,
  "message": "请求成功",
  "full_response": "根据您提供的文件，我分析了以下内容...",
  "response_length": 150,
  "bot_name": "GPT-3.5-Turbo",
  "attachments_processed": 2,
  "attachment_info": [
    {
      "url": "https://example.com/document.pdf",
      "content_type": "application/pdf",
      "name": "document.pdf",
      "parsed_content": "文件内容摘要..."
    }
  ]
}
```

### POST /chat/receive-files

调用生成类机器人（图像、视频、音频生成）并接收生成的文件

**注意**: 此接口专注于单次生成任务，不支持多轮会话功能。

**请求体:**
```json
{
  "role": "user",
  "content": "Generate a beautiful landscape image with mountains and a lake",
  "bot_name": "Imagen-4-Ultra-Exp",
  "api_key": "your_poe_api_key_here"
}
```

**响应:**
```json
{
  "success": true,
  "message": "请求成功",
  "full_response": "I've generated a beautiful landscape image for you.",
  "response_length": 45,
  "bot_name": "Imagen-4-Ultra-Exp",
  "received_files": [
    {
      "url": "https://example.com/generated_image.png",
      "content_type": "image/png",
      "name": "generated_image.png",
      "size": 1024000
    }
  ],
  "files_count": 1
}
```

### GET /health

健康检查接口

**响应:**
```json
{
  "status": "healthy",
  "proxy_enabled": true,
  "api_key_configured": true
}
```

## 接口功能对比

| 接口 | system_prompt支持 | 多轮会话支持 | 文件支持 | 主要用途 |
|------|------------------|-------------|----------|----------|
| `/chat` | ✅ | ❌ | ❌ | 单轮对话，角色设定 |
| `/chat/with-files` | ❌ | ✅ | ✅ | 多轮对话，文件处理 |
| `/chat/receive-files` | ❌ | ❌ | ❌ | 生成类机器人，文件接收 |

### 使用建议

- **简单问答 + 角色设定**: 使用 `/chat` 接口，通过 `system_prompt` 设置AI角色
- **连续对话 + 文件分析**: 使用 `/chat/with-files` 接口，支持对话历史和文件上传
- **图像/视频生成**: 使用 `/chat/receive-files` 接口，专门用于接收AI生成的文件

## 测试

运行基础API测试脚本：

```bash
python test_api.py
```

运行文件聊天接口测试脚本：

```bash
python test_file_chat.py
```

运行文件接收接口测试脚本：

```bash
python test_receive_files.py
```

运行文件接收示例：

```bash
python example_receive_files.py
```

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| POE_API_KEY | Poe API 密钥 | 必需 |
| USE_PROXY | 是否使用代理 | true |
| PROXY_HOST | 代理主机 | 127.0.0.1 |
| PROXY_PORT | 代理端口 | 10300 |
| SERVER_HOST | 服务器主机 | 0.0.0.0 |
| SERVER_PORT | 服务器端口 | 8081 |
| LOG_LEVEL | 日志级别 | info |

### 支持的机器人

- GPT-3.5-Turbo
- GPT-4
- Claude-3-Haiku
- Claude-3-Sonnet
- Claude-3-Opus
- 以及 Poe 上的其他机器人

## 开发

### 添加新的路由

1. 在 `app/routers/` 目录下创建新的路由文件
2. 在 `app/main.py` 中导入并注册路由

### 添加新的服务

1. 在 `app/services/` 目录下创建新的服务文件
2. 在需要的地方导入并使用服务

## 部署

### 生产环境

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8081 --workers 4
```

### Docker 部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8081"]
```

## 🎉 功能总结

本项目已实现了完整的Poe API服务，具备以下核心能力：

### 🎭 system_prompt 角色设定功能
- ✅ 为AI设置角色和行为模式
- ✅ 支持各种专业场景（编程导师、诗人、科学家等）
- ✅ 可控制回答格式和风格
- ✅ 完全向后兼容，不影响现有使用

### 💬 多轮会话支持
- ✅ AI能记住之前的对话内容
- ✅ 支持复杂的上下文对话
- ✅ 自动管理对话历史
- ✅ 支持历史消息中的文件附件

### 📁 文件处理能力
- ✅ 支持文件上传和AI分析
- ✅ 支持接收AI生成的图片、视频等文件
- ✅ 自动处理各种文件格式
- ✅ 提供详细的文件信息反馈

### 🛡️ 完善的错误处理
- ✅ 统一的错误响应格式
- ✅ 友好的错误提示信息
- ✅ 不会因业务错误返回HTTP 500
- ✅ 完整的异常捕获和处理

### 🔧 技术特性
- ✅ 基于FastAPI的高性能架构
- ✅ 完整的API文档和类型提示
- ✅ 标准的REST API设计
- ✅ 支持代理配置和环境变量管理

通过这些功能，用户可以轻松构建各种AI应用场景，从简单的问答到复杂的多轮对话，从文本处理到文件分析，从角色扮演到专业咨询，满足不同的业务需求。

## 许可证

MIT License
