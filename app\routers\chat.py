"""
聊天相关路由
"""

from fastapi import APIRouter, HTTPException
from app.models.chat import (
    ChatRequest, ChatResponse, HealthResponse, ServiceInfoResponse,
    ChatWithFilesRequest, ChatWithFilesResponse,
    ChatReceiveFilesRequest, ChatReceiveFilesResponse,
    RateLimitRequest, RateLimitResponse
)
from app.services.poe_service import poe_service
from app.config import settings
from app.utils.rate_limiter import file_upload_limiter

router = APIRouter()


@router.get("/", response_model=ServiceInfoResponse)
async def get_service_info():
    """获取服务信息"""
    return ServiceInfoResponse(
        service=settings.app_name,
        status="运行中",
        version=settings.app_version,
        proxy_enabled=settings.use_proxy,
        proxy_url=settings.proxy_url,
        endpoints={
            "chat": "POST /chat - 发送聊天请求",
            "chat_with_files": "POST /chat/with-files - 发送带文件的聊天请求",
            "chat_receive_files": "POST /chat/receive-files - 调用生成类机器人并接收文件",
            "health": "GET /health - 健康检查",
            "docs": "GET /docs - API 文档"
        }
    )


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy",
        proxy_enabled=settings.use_proxy,
        api_key_configured=True  # API Key 现在通过请求参数传递
    )


@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    聊天接口
    
    发送消息到指定的 Poe 机器人并获取响应
    """
    
    # 验证参数
    if not request.content.strip():
        raise HTTPException(status_code=400, detail="消息内容不能为空")

    if not request.api_key.strip():
        raise HTTPException(status_code=400, detail="API Key 不能为空")

    if request.role not in ["user", "bot", "system"]:
        raise HTTPException(
            status_code=400,
            detail="role 必须是 'user', 'bot' 或 'system'"
        )

    # 发送请求
    result = await poe_service.chat(
        role=request.role,
        content=request.content,
        bot_name=request.bot_name,
        api_key=request.api_key,
        system_prompt=request.system_prompt
    )
    
    # 不管成功还是失败，都返回结果，让前端处理
    # 只有在真正的服务器错误时才返回500
    return ChatResponse(**result)


@router.post("/chat/with-files", response_model=ChatWithFilesResponse)
async def chat_with_files(request: ChatWithFilesRequest):
    """
    支持文件的聊天接口

    发送消息和文件到指定的 Poe 机器人并获取响应
    """

    # 验证参数
    if not request.content.strip():
        raise HTTPException(status_code=400, detail="消息内容不能为空")

    if not request.api_key.strip():
        raise HTTPException(status_code=400, detail="API Key 不能为空")

    if request.role not in ["user", "bot", "system"]:
        raise HTTPException(
            status_code=400,
            detail="role 必须是 'user', 'bot' 或 'system'"
        )

    # 验证文件URL
    if request.file_urls:
        for file_url in request.file_urls:
            if not file_url.strip():
                raise HTTPException(status_code=400, detail="文件URL不能为空")
            if not (file_url.startswith('http://') or file_url.startswith('https://')):
                raise HTTPException(status_code=400, detail="文件URL必须是有效的HTTP/HTTPS链接")

    # 转换对话历史格式
    conversation_history = None
    if request.conversation_history:
        conversation_history = [
            {
                "role": msg.role,
                "content": msg.content,
                "attachments": msg.attachments
            }
            for msg in request.conversation_history
        ]

    # 发送请求
    result = await poe_service.chat_with_files(
        role=request.role,
        content=request.content,
        bot_name=request.bot_name,
        api_key=request.api_key,
        file_urls=request.file_urls,
        conversation_history=conversation_history,
        system_prompt=request.system_prompt
    )

    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["message"])

    return ChatWithFilesResponse(**result)


@router.post("/chat/receive-files", response_model=ChatReceiveFilesResponse)
async def chat_receive_files(request: ChatReceiveFilesRequest):
    """
    调用生成类机器人并接收文件

    调用图像生成、视频生成、音频生成等机器人，并接收它们生成的文件
    """

    # 验证参数
    if not request.content.strip():
        raise HTTPException(status_code=400, detail="消息内容不能为空")

    if not request.api_key.strip():
        raise HTTPException(status_code=400, detail="API Key 不能为空")

    if request.role not in ["user", "bot", "system"]:
        raise HTTPException(
            status_code=400,
            detail="role 必须是 'user', 'bot' 或 'system'"
        )

    # 发送请求
    result = await poe_service.chat_receive_files(
        role=request.role,
        content=request.content,
        bot_name=request.bot_name,
        api_key=request.api_key
    )

    # 不管成功还是失败，都返回结果，让前端处理
    # 只有在真正的服务器错误时才返回500
    return ChatReceiveFilesResponse(**result)


@router.post("/rate-limit/status", response_model=RateLimitResponse)
async def get_rate_limit_status(request: RateLimitRequest):
    """
    查询文件上传限流状态

    返回指定API key的文件上传限流配额信息
    """

    # 验证参数
    if not request.api_key.strip():
        raise HTTPException(status_code=400, detail="API Key 不能为空")

    # 获取限流状态
    quota_info = await file_upload_limiter.get_remaining_quota(request.api_key)

    return RateLimitResponse(**quota_info)
