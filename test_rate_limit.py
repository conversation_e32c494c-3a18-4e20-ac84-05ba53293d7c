"""
测试文件上传限流功能
"""

import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# 服务器配置
BASE_URL = "http://localhost:8081"
API_KEY = "FO-voFR2Z8RH_v0TDliivxp7RvfRhJCTqdJkAcpymgw"  # 请替换为实际的API key

def test_rate_limit_status():
    """测试限流状态查询"""
    print("=== 测试限流状态查询 ===")
    
    url = f"{BASE_URL}/rate-limit/status"
    payload = {
        "api_key": API_KEY
    }
    
    response = requests.post(url, json=payload)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"限流状态: {json.dumps(data, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    print()

def test_file_upload_rate_limit():
    """测试文件上传限流"""
    print("=== 测试文件上传限流 ===")
    
    url = f"{BASE_URL}/chat/with-files"
    
    # 测试文件URL（这里使用一个示例URL，实际测试时请使用有效的文件URL）
    test_file_url = "https://sns-video-alos.xhscdn.com/stream/79/110/258/01e805bd184b05f04f03700196566c2b6a_258.mp4"
    
    payload = {
        "role": "user",
        "content": "请分析这个文件",
        "bot_name": "review_film_bot",
        "api_key": API_KEY,
        "file_urls": [test_file_url]
    }
    
    # 连续发送多个请求测试限流
    for i in range(7):  # 发送7次请求，超过限制的5次
        print(f"第 {i+1} 次请求...")
        
        # 查询当前限流状态
        test_rate_limit_status()
        
        response = requests.post(url, json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"请求成功: 处理了 {data.get('attachments_processed', 0)} 个文件")
            else:
                print(f"请求失败: {data.get('message', '未知错误')}")
        else:
            print(f"HTTP错误: {response.text}")
        
        print("-" * 50)
        
        # 如果不是最后一次请求，等待一小段时间
        if i < 6:
            time.sleep(2)

def test_no_file_upload():
    """测试不上传文件的请求（不应该被限流）"""
    print("=== 测试无文件上传请求 ===")
    
    url = f"{BASE_URL}/chat/with-files"
    payload = {
        "role": "user",
        "content": "你好，这是一个测试消息",
        "bot_name": "GPT-3.5-Turbo",
        "api_key": API_KEY
        # 注意：没有 file_urls 字段
    }
    
    # 发送多个无文件的请求
    for i in range(3):
        print(f"无文件请求 {i+1}...")
        response = requests.post(url, json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"请求结果: success={data.get('success')}")
        else:
            print(f"错误: {response.text}")
        
        time.sleep(1)
    
    print()

def single_file_upload_request(thread_id, api_key, file_url):
    """单个文件上传请求（用于多线程测试）"""
    url = f"{BASE_URL}/chat/with-files"
    payload = {
        "role": "user",
        "content": f"线程{thread_id}: 请分析这个文件",
        "bot_name": "review_film_bot",
        "api_key": api_key,
        "file_urls": [file_url]
    }

    start_time = time.time()
    try:
        response = requests.post(url, json=payload, timeout=30)
        end_time = time.time()

        result = {
            "thread_id": thread_id,
            "status_code": response.status_code,
            "response_time": round(end_time - start_time, 2),
            "timestamp": time.strftime("%H:%M:%S", time.localtime(start_time))
        }

        if response.status_code == 200:
            data = response.json()
            result["success"] = data.get("success", False)
            result["message"] = data.get("message", "")
            result["attachments_processed"] = data.get("attachments_processed", 0)
        else:
            result["success"] = False
            result["message"] = f"HTTP {response.status_code}: {response.text[:100]}"
            result["attachments_processed"] = 0

        return result

    except Exception as e:
        end_time = time.time()
        return {
            "thread_id": thread_id,
            "status_code": 0,
            "response_time": round(end_time - start_time, 2),
            "timestamp": time.strftime("%H:%M:%S", time.localtime(start_time)),
            "success": False,
            "message": f"请求异常: {str(e)}",
            "attachments_processed": 0
        }

def test_concurrent_file_upload():
    """测试并发文件上传限流"""
    print("=== 测试并发文件上传限流 ===")

    test_file_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"

    # 先查询初始限流状态
    print("初始限流状态:")
    test_rate_limit_status()

    # 并发参数
    thread_count = 8  # 并发线程数
    print(f"启动 {thread_count} 个并发线程进行文件上传测试...")
    print(f"预期结果: 前5个请求成功，后3个请求被限流")
    print("-" * 60)

    # 使用线程池执行并发请求
    with ThreadPoolExecutor(max_workers=thread_count) as executor:
        # 提交所有任务
        futures = []
        for i in range(thread_count):
            future = executor.submit(single_file_upload_request, i+1, API_KEY, test_file_url)
            futures.append(future)

        # 收集结果
        results = []
        for future in as_completed(futures):
            result = future.result()
            results.append(result)

    # 按线程ID排序结果
    results.sort(key=lambda x: x["thread_id"])

    # 打印结果
    print(f"{'线程':<4} {'时间':<8} {'状态码':<6} {'成功':<4} {'耗时(s)':<8} {'消息'}")
    print("-" * 80)

    success_count = 0
    rate_limited_count = 0
    error_count = 0

    for result in results:
        status_icon = "✅" if result["success"] else "❌"
        message = result["message"][:50] + "..." if len(result["message"]) > 50 else result["message"]

        print(f"{result['thread_id']:<4} {result['timestamp']:<8} {result['status_code']:<6} "
              f"{status_icon:<4} {result['response_time']:<8} {message}")

        if result["success"]:
            success_count += 1
        elif "文件上传过于频繁" in result["message"]:
            rate_limited_count += 1
        else:
            error_count += 1

    print("-" * 80)
    print(f"测试结果统计:")
    print(f"  成功请求: {success_count}")
    print(f"  限流请求: {rate_limited_count}")
    print(f"  错误请求: {error_count}")
    print(f"  总请求数: {len(results)}")

    # 测试后查询限流状态
    print("\n测试后限流状态:")
    test_rate_limit_status()

def test_rate_limit_recovery():
    """测试限流恢复"""
    print("=== 测试限流恢复 ===")

    # 先触发限流
    print("1. 先快速发送请求触发限流...")
    test_file_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"

    # 快速发送6个请求
    for i in range(6):
        result = single_file_upload_request(i+1, API_KEY, test_file_url)
        status = "成功" if result["success"] else "失败"
        print(f"  请求{i+1}: {status} - {result['message'][:30]}...")
        time.sleep(0.1)  # 很短的间隔

    print("\n2. 查询当前限流状态:")
    test_rate_limit_status()

    print("\n3. 等待限流窗口重置...")
    # 等待一段时间让限流窗口滑动
    wait_time = 65  # 等待65秒，确保窗口重置
    for remaining in range(wait_time, 0, -5):
        print(f"  剩余等待时间: {remaining} 秒")
        time.sleep(5)

    print("\n4. 限流恢复后再次测试:")
    test_rate_limit_status()

    # 再次发送请求验证恢复
    result = single_file_upload_request(999, API_KEY, test_file_url)
    status = "成功" if result["success"] else "失败"
    print(f"恢复测试结果: {status} - {result['message']}")

if __name__ == "__main__":
    print("文件上传限流功能测试")
    print("=" * 50)
    print(f"服务器地址: {BASE_URL}")
    print(f"使用API Key: {API_KEY[:10]}...")
    print()

    # 测试选项
    print("请选择测试模式:")
    print("1. 基础测试 (顺序发送请求)")
    print("2. 并发测试 (多线程同时发送)")
    print("3. 恢复测试 (测试限流恢复)")
    print("4. 完整测试 (包含所有测试)")
    print("5. 仅查询限流状态")

    try:
        choice = input("\n请输入选择 (1-5): ").strip()
    except KeyboardInterrupt:
        print("\n测试已取消")
        exit()

    print("\n" + "=" * 50)

    if choice == "1":
        print("执行基础测试...")
        test_rate_limit_status()
        test_no_file_upload()
        test_file_upload_rate_limit()

    elif choice == "2":
        print("执行并发测试...")
        test_concurrent_file_upload()

    elif choice == "3":
        print("执行恢复测试...")
        test_rate_limit_recovery()

    elif choice == "4":
        print("执行完整测试...")
        print("\n--- 1. 基础功能测试 ---")
        test_rate_limit_status()
        test_no_file_upload()

        print("\n--- 2. 并发限流测试 ---")
        test_concurrent_file_upload()

        print("\n--- 3. 限流恢复测试 ---")
        confirm = input("\n是否执行恢复测试？(需要等待65秒) [y/N]: ").strip().lower()
        if confirm == 'y':
            test_rate_limit_recovery()
        else:
            print("跳过恢复测试")

    elif choice == "5":
        print("查询限流状态...")
        test_rate_limit_status()

    else:
        print("无效选择，执行基础测试...")
        test_rate_limit_status()
        test_file_upload_rate_limit()

    print("\n" + "=" * 50)
    print("测试完成！")

    # 最终状态
    print("\n=== 最终限流状态 ===")
    test_rate_limit_status()
