"""
测试文件上传限流功能
"""

import requests
import json
import time

# 服务器配置
BASE_URL = "http://localhost:8081"
API_KEY = "FO-voFR2Z8RH_v0TDliivxp7RvfRhJCTqdJkAcpymgw"  # 请替换为实际的API key

def test_rate_limit_status():
    """测试限流状态查询"""
    print("=== 测试限流状态查询 ===")
    
    url = f"{BASE_URL}/rate-limit/status"
    payload = {
        "api_key": API_KEY
    }
    
    response = requests.post(url, json=payload)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"限流状态: {json.dumps(data, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    print()

def test_file_upload_rate_limit():
    """测试文件上传限流"""
    print("=== 测试文件上传限流 ===")
    
    url = f"{BASE_URL}/chat/with-files"
    
    # 测试文件URL（这里使用一个示例URL，实际测试时请使用有效的文件URL）
    test_file_url = "https://example.com/test.txt"
    
    payload = {
        "role": "user",
        "content": "请分析这个文件",
        "bot_name": "GPT-3.5-Turbo",
        "api_key": API_KEY,
        "file_urls": [test_file_url]
    }
    
    # 连续发送多个请求测试限流
    for i in range(7):  # 发送7次请求，超过限制的5次
        print(f"第 {i+1} 次请求...")
        
        # 查询当前限流状态
        test_rate_limit_status()
        
        response = requests.post(url, json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"请求成功: 处理了 {data.get('attachments_processed', 0)} 个文件")
            else:
                print(f"请求失败: {data.get('message', '未知错误')}")
        else:
            print(f"HTTP错误: {response.text}")
        
        print("-" * 50)
        
        # 如果不是最后一次请求，等待一小段时间
        if i < 6:
            time.sleep(2)

def test_no_file_upload():
    """测试不上传文件的请求（不应该被限流）"""
    print("=== 测试无文件上传请求 ===")
    
    url = f"{BASE_URL}/chat/with-files"
    payload = {
        "role": "user",
        "content": "你好，这是一个测试消息",
        "bot_name": "GPT-3.5-Turbo",
        "api_key": API_KEY
        # 注意：没有 file_urls 字段
    }
    
    # 发送多个无文件的请求
    for i in range(3):
        print(f"无文件请求 {i+1}...")
        response = requests.post(url, json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"请求结果: success={data.get('success')}")
        else:
            print(f"错误: {response.text}")
        
        time.sleep(1)
    
    print()

if __name__ == "__main__":
    print("开始测试文件上传限流功能...")
    print(f"使用API Key: {API_KEY[:10]}...")
    print()
    
    # 首先查询初始状态
    test_rate_limit_status()
    
    # 测试无文件上传请求
    test_no_file_upload()
    
    # 测试文件上传限流
    test_file_upload_rate_limit()
    
    # 最后再查询一次状态
    print("=== 最终限流状态 ===")
    test_rate_limit_status()
    
    print("测试完成！")
