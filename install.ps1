# Poe Connection 安装脚本
# 用于安装构建好的 wheel 包

param(
    [switch]$Dev,  # 是否安装开发依赖
    [switch]$Force  # 是否强制重新安装
)

Write-Host "📦 Poe Connection 安装脚本" -ForegroundColor Green

# 查找最新的 wheel 文件
Write-Host "🔍 查找 wheel 文件..." -ForegroundColor Yellow
if (-not (Test-Path "dist")) {
    Write-Host "❌ dist 目录不存在，请先运行构建脚本" -ForegroundColor Red
    exit 1
}

$wheelFiles = Get-ChildItem -Path "dist" -Filter "*.whl" | Sort-Object LastWriteTime -Descending
if ($wheelFiles.Count -eq 0) {
    Write-Host "❌ 未找到 wheel 文件，请先运行构建脚本" -ForegroundColor Red
    exit 1
}

$wheelFile = $wheelFiles[0]
Write-Host "📦 找到文件: $($wheelFile.Name)" -ForegroundColor Green

# 准备安装参数
$installArgs = @()
$installArgs += $wheelFile.FullName

if ($Dev) {
    $installArgs += "[dev]"
    Write-Host "🔧 将安装开发依赖" -ForegroundColor Yellow
}

if ($Force) {
    $installArgs += "--force-reinstall"
    Write-Host "🔄 将强制重新安装" -ForegroundColor Yellow
}

# 执行安装
Write-Host "⚡ 开始安装..." -ForegroundColor Yellow
try {
    $installCommand = "pip install " + ($installArgs -join " ")
    Write-Host "🔨 执行: $installCommand" -ForegroundColor Gray
    
    Invoke-Expression $installCommand
    Write-Host "✅ 安装成功！" -ForegroundColor Green
}
catch {
    Write-Host "❌ 安装失败：$_" -ForegroundColor Red
    exit 1
}

# 验证安装
Write-Host "🔍 验证安装..." -ForegroundColor Yellow
try {
    $version = pip show poe-connection | Select-String "Version:" | ForEach-Object { $_.ToString().Split(":")[1].Trim() }
    Write-Host "✅ poe-connection $version 已安装" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  无法验证安装" -ForegroundColor Yellow
}

# 测试命令行工具
Write-Host "🧪 测试命令行工具..." -ForegroundColor Yellow
try {
    poe-connection --help | Out-Null
    Write-Host "✅ 命令行工具可用" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  命令行工具可能不可用，请检查 PATH 环境变量" -ForegroundColor Yellow
}

Write-Host "`n🎉 安装完成！" -ForegroundColor Green
Write-Host "💡 使用方法：" -ForegroundColor Cyan
Write-Host "  • 命令行：poe-connection --help" -ForegroundColor White
Write-Host "  • 启动服务：poe-connection --host 0.0.0.0 --port 8000" -ForegroundColor White
Write-Host "  • 开发模式：poe-connection --dev" -ForegroundColor White
Write-Host "  • 生产模式：poe-connection --prod" -ForegroundColor White
