# Poe Connection 使用示例

## 安装后的使用方法

### 1. 基本启动

```powershell
# 使用默认配置启动服务
poe-connection

# 输出示例：
# ✅ 代理已启用: http://127.0.0.1:10300
# 🚀 启动 Poe API 服务 v1.0.0
# 📡 地址: http://0.0.0.0:8081
# 🔗 代理: 启用
# 🌐 代理地址: http://127.0.0.1:10300
# 🤖 API Key: 通过请求参数传递
# 📖 API 文档: http://localhost:8081/docs
# 📊 日志级别: info
# 👥 工作进程: 1
# --------------------------------------------------
```

### 2. 开发模式

```powershell
# 启动开发模式（热重载 + 调试日志）
poe-connection --dev

# 输出示例：
# ✅ 代理已启用: http://127.0.0.1:10300
# 🔧 开发模式已启用
# 🚀 启动 Poe API 服务 v1.0.0
# 📡 地址: http://0.0.0.0:8081
# 🔗 代理: 启用
# 🌐 代理地址: http://127.0.0.1:10300
# 🤖 API Key: 通过请求参数传递
# 📖 API 文档: http://localhost:8081/docs
# 📊 日志级别: debug
# 🔥 热重载: 启用
# --------------------------------------------------
```

### 3. 生产模式

```powershell
# 启动生产模式（多进程，无热重载）
poe-connection --prod --workers 4

# 输出示例：
# ✅ 代理已启用: http://127.0.0.1:10300
# 🚀 生产模式已启用
# 🚀 启动 Poe API 服务 v1.0.0
# 📡 地址: http://0.0.0.0:8081
# 🔗 代理: 启用
# 🌐 代理地址: http://127.0.0.1:10300
# 🤖 API Key: 通过请求参数传递
# 📖 API 文档: http://localhost:8081/docs
# 📊 日志级别: info
# 👥 工作进程: 4
# --------------------------------------------------
```

### 4. 自定义配置

```powershell
# 自定义主机和端口
poe-connection --host 127.0.0.1 --port 9000

# 自定义日志级别
poe-connection --log-level debug

# 启用热重载
poe-connection --reload
```

### 5. 查看帮助

```powershell
poe-connection --help

# 输出：
# usage: poe-connection [-h] [--host HOST] [--port PORT] [--reload] 
#                       [--workers WORKERS] [--log-level {critical,error,warning,info,debug,trace}] 
#                       [--dev] [--prod]
# 
# 启动 Poe API 服务
# 
# options:
#   -h, --help            show this help message and exit
#   --host HOST           服务器主机地址 (默认: 0.0.0.0)
#   --port PORT           服务器端口 (默认: 8081)
#   --reload              启用热重载 (开发模式)
#   --workers WORKERS     工作进程数量 (生产模式，默认: 1)
#   --log-level {critical,error,warning,info,debug,trace}
#                         日志级别 (默认: info)
#   --dev                 开发模式 (等同于 --reload --log-level debug)
#   --prod                生产模式 (禁用热重载，使用多进程)
```

## API 使用

服务启动后，可以通过以下方式访问：

### 1. API 文档

- Swagger UI: http://localhost:8081/docs
- ReDoc: http://localhost:8081/redoc

### 2. API 端点

- 聊天接口: `POST /chat`
- 健康检查: `GET /`

### 3. 使用示例

```bash
# 发送聊天请求
curl -X POST "http://localhost:8081/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, world!",
    "api_key": "your_poe_api_key",
    "bot": "Claude-3-Haiku"
  }'
```

## 环境配置

如果需要自定义配置，可以创建 `.env` 文件：

```env
# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8081

# 代理配置
USE_PROXY=true
PROXY_URL=http://127.0.0.1:10300

# 日志配置
LOG_LEVEL=info

# 应用信息
APP_NAME=Poe API 服务
APP_VERSION=1.0.0
```

## 停止服务

在终端中按 `Ctrl+C` 停止服务：

```
^C
👋 服务已停止
```

## 卸载

```powershell
pip uninstall poe-connection
```
