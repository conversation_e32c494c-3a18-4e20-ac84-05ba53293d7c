# 文件接收接口文档

## 概述

`/chat/receive-files` 接口专门用于调用Poe平台上的生成类机器人（如图像生成、视频生成、音频生成等），并接收它们生成的文件。

**注意**: 此接口专注于单次生成任务，不支持多轮会话功能。如需多轮会话，请使用 `/chat/with-files` 接口。

## 功能特性

- 🎨 支持图像生成机器人（Imagen-4-Ultra-Exp, Imagen-3, Midjourney等）
- 🎬 支持视频生成机器人
- 🎵 支持音频生成机器人
- 📄 自动接收和处理生成的文件
- 🔗 返回可直接访问的文件URL
- 📊 提供详细的文件信息

## API 接口

### POST /chat/receive-files

#### 请求参数

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| role | string | 是 | 角色，支持 "user", "bot", "system" |
| content | string | 是 | 生成指令/描述 |
| bot_name | string | 否 | 生成机器人名称，默认 "Imagen-4-Ultra-Exp" |
| api_key | string | 是 | Poe API 密钥 |

#### 请求示例

```json
{
  "role": "user",
  "content": "Generate a beautiful landscape image with mountains and a lake at sunset",
  "bot_name": "Imagen-4-Ultra-Exp",
  "api_key": "your_poe_api_key_here"
}
```

#### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| message | string | 状态消息 |
| full_response | string | 机器人的文本响应 |
| response_length | integer | 文本响应长度 |
| bot_name | string | 使用的机器人名称 |
| received_files | array | 接收到的文件列表 |
| files_count | integer | 接收到的文件数量 |

#### 文件信息结构

```json
{
  "url": "https://example.com/generated_file.png",
  "content_type": "image/png",
  "name": "generated_file.png",
  "size": 1024000
}
```

## 支持的生成机器人

### 图像生成
- **Imagen-4-Ultra-Exp**: OpenAI的图像生成模型
- **Imagen-3**: Google的图像生成模型
- **Midjourney**: 艺术风格图像生成
- **Stable Diffusion**: 开源图像生成模型

### 视频生成
- **Sora**: OpenAI的视频生成模型（如果可用）
- **其他视频生成机器人**

### 音频生成
- **音乐生成机器人**
- **语音合成机器人**

## 使用场景

### 1. 图像生成
```python
payload = {
    "role": "user",
    "content": "Create a photorealistic image of a futuristic city with flying cars",
    "bot_name": "Imagen-4-Ultra-Exp",
    "api_key": "your_api_key"
}
```

### 2. 艺术创作
```python
payload = {
    "role": "user",
    "content": "Generate abstract art with vibrant colors and geometric patterns",
    "bot_name": "Midjourney",
    "api_key": "your_api_key"
}
```

### 3. 概念设计
```python
payload = {
    "role": "user",
    "content": "Design a logo for a tech startup focused on AI and sustainability",
    "bot_name": "Imagen-4-Ultra-Exp",
    "api_key": "your_api_key"
}
```

## 文件处理

### 文件类型支持
- **图像**: PNG, JPEG, GIF, WebP
- **视频**: MP4, AVI, MOV, WebM
- **音频**: MP3, WAV, AAC, OGG

### 文件访问
生成的文件通过URL直接访问：
```python
import requests

# 下载生成的文件
file_url = response_data['received_files'][0]['url']
file_response = requests.get(file_url)

with open('generated_image.png', 'wb') as f:
    f.write(file_response.content)
```

## 错误处理

### 常见错误

1. **生成失败**
```json
{
  "success": false,
  "message": "图像生成失败: 内容违反使用政策",
  "files_count": 0
}
```

2. **机器人不可用**
```json
{
  "success": false,
  "message": "请求失败: 机器人 'NonExistentBot' 不存在",
  "files_count": 0
}
```

3. **API限制**
```json
{
  "success": false,
  "message": "请求失败: API使用额度不足",
  "files_count": 0
}
```

## 最佳实践

### 1. 提示词优化
- 使用详细、具体的描述
- 包含风格、颜色、构图等细节
- 避免模糊或矛盾的指令

### 2. 性能优化
- 设置合适的超时时间（图像生成通常需要30-120秒）
- 实现重试机制处理临时失败
- 批量处理时添加适当的延迟

### 3. 成本控制
- 监控API使用量
- 缓存生成结果避免重复请求
- 使用合适的机器人模型

## 完整示例

```python
import requests
import json

def generate_image(prompt, bot_name="Imagen-4-Ultra-Exp"):
    url = "http://localhost:8081/chat/receive-files"
    
    payload = {
        "role": "user",
        "content": prompt,
        "bot_name": bot_name,
        "api_key": "your_poe_api_key_here"
    }
    
    try:
        response = requests.post(
            url, 
            json=payload,
            timeout=120
        )
        
        if response.status_code == 200:
            data = response.json()
            
            if data['success'] and data['files_count'] > 0:
                print(f"✅ 生成成功! 共 {data['files_count']} 个文件")
                
                for file_info in data['received_files']:
                    print(f"📄 {file_info['name']}")
                    print(f"🔗 {file_info['url']}")
                
                return data['received_files']
            else:
                print(f"❌ 生成失败: {data['message']}")
                return []
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return []

# 使用示例
files = generate_image("A serene mountain landscape at sunrise")
```

## 注意事项

1. **内容政策**: 确保生成内容符合平台使用政策
2. **版权问题**: 注意生成内容的版权和使用权限
3. **文件有效期**: 生成的文件URL可能有时效性
4. **网络稳定**: 确保网络连接稳定，生成过程可能较长
5. **存储空间**: 及时下载和备份重要的生成文件

## 测试和调试

运行测试脚本：
```bash
# 基础功能测试
python test_receive_files.py

# 交互式示例
python example_receive_files.py
```

查看API文档：
```
http://localhost:8081/docs
```
