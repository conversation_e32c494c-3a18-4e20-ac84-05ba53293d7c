# Poe Connection 构建脚本
# 用于构建 wheel 包

Write-Host "🚀 开始构建 Poe Connection..." -ForegroundColor Green

# 检查必要工具
Write-Host "📦 检查构建工具..." -ForegroundColor Yellow
$tools = @("pip", "python")
foreach ($tool in $tools) {
    try {
        & $tool --version | Out-Null
        Write-Host "✅ $tool 已安装" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ $tool 未找到，请先安装" -ForegroundColor Red
        exit 1
    }
}

# 安装/升级构建依赖
Write-Host "📦 安装构建依赖..." -ForegroundColor Yellow
pip install --upgrade pip setuptools wheel build twine

# 清理之前的构建文件
Write-Host "🧹 清理之前的构建文件..." -ForegroundColor Yellow
$cleanupPaths = @("build", "dist", "*.egg-info")
foreach ($path in $cleanupPaths) {
    if (Test-Path $path) {
        Remove-Item -Recurse -Force $path
        Write-Host "🗑️  删除 $path" -ForegroundColor Gray
    }
}

# 构建包
Write-Host "🔨 构建包..." -ForegroundColor Yellow
try {
    python -m build
    Write-Host "✅ 构建成功！" -ForegroundColor Green
}
catch {
    Write-Host "❌ 构建失败：$_" -ForegroundColor Red
    exit 1
}

# 验证构建的包
Write-Host "🔍 验证构建的包..." -ForegroundColor Yellow
try {
    twine check dist/*
    Write-Host "✅ 包验证通过！" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  包验证有警告：$_" -ForegroundColor Yellow
}

# 显示构建结果
Write-Host "`n📋 构建结果：" -ForegroundColor Cyan
if (Test-Path "dist") {
    Get-ChildItem -Path "dist" | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 2)
        Write-Host "  📦 $($_.Name) (${size} KB)" -ForegroundColor White
    }
}

Write-Host "`n🎉 构建完成！" -ForegroundColor Green
Write-Host "📁 文件位于 dist/ 目录" -ForegroundColor Gray
Write-Host "💡 使用 'pip install dist/*.whl' 安装" -ForegroundColor Gray
