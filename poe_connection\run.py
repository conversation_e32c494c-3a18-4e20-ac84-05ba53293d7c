#!/usr/bin/env python3
"""
Poe API 服务启动脚本
"""

import argparse
import sys
import uvicorn
from app.config import settings


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动 Poe API 服务")
    
    # 添加命令行参数
    parser.add_argument(
        "--host", 
        default=settings.server_host, 
        help=f"服务器主机地址 (默认: {settings.server_host})"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=settings.server_port, 
        help=f"服务器端口 (默认: {settings.server_port})"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="启用热重载 (开发模式)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=1, 
        help="工作进程数量 (生产模式，默认: 1)"
    )
    parser.add_argument(
        "--log-level", 
        default=settings.log_level, 
        choices=["critical", "error", "warning", "info", "debug", "trace"],
        help=f"日志级别 (默认: {settings.log_level})"
    )
    parser.add_argument(
        "--dev", 
        action="store_true", 
        help="开发模式 (等同于 --reload --log-level debug)"
    )
    parser.add_argument(
        "--prod", 
        action="store_true", 
        help="生产模式 (禁用热重载，使用多进程)"
    )
    
    args = parser.parse_args()
    
    # 处理模式设置
    if args.dev:
        args.reload = True
        args.log_level = "debug"
        args.workers = 1
        print("🔧 开发模式已启用")
    
    if args.prod:
        args.reload = False
        args.workers = max(args.workers, 4)  # 生产模式至少4个worker
        print("🚀 生产模式已启用")
    
    # 显示启动信息
    print(f"🚀 启动 {settings.app_name} v{settings.app_version}")
    print(f"📡 地址: http://{args.host}:{args.port}")
    print(f"🔗 代理: {'启用' if settings.use_proxy else '禁用'}")
    if settings.use_proxy:
        print(f"🌐 代理地址: {settings.proxy_url}")
    print(f"🤖 API Key: 通过请求参数传递")
    print(f"📖 API 文档: http://localhost:{args.port}/docs")
    print(f"📊 日志级别: {args.log_level}")
    
    if args.reload:
        print("🔥 热重载: 启用")
    else:
        print(f"👥 工作进程: {args.workers}")
    
    print("-" * 50)
    
    # 启动服务器
    try:
        uvicorn.run(
            "app.main:app",
            host=args.host,
            port=args.port,
            log_level=args.log_level,
            reload=args.reload,
            workers=args.workers if not args.reload else 1,  # reload模式下只能用1个worker
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
