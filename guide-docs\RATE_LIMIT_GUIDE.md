# 文件上传限流功能说明

## 概述

为了防止文件上传接口被滥用，系统对 `chat_with_files` 接口中的文件上传操作实施了限流控制。

## 限流规则

- **限流对象**: 基于 API Key 进行限流
- **限流范围**: 仅对实际调用 `fp.upload_file` 的操作计数
- **限流策略**: 每分钟最多允许 5 次文件上传
- **时间窗口**: 60秒滑动窗口

## 重要特性

### 1. 精确限流
- ✅ 只有调用 `fp.upload_file` 时才计入限流
- ✅ 不上传文件的聊天请求不受限制
- ✅ 支持历史消息中的附件上传限流

### 2. 智能处理
- 🔍 在文件上传前检查限流状态
- ⚡ 超出限制时立即返回错误，不浪费资源
- 📊 提供详细的限流状态信息

## API 接口

### 1. 文件上传接口 (原有接口)
```
POST /chat/with-files
```

**限流行为变化:**
- 当文件上传超出限制时，返回错误信息而不是 HTTP 500
- 错误消息包含剩余等待时间

**示例错误响应:**
```json
{
  "success": false,
  "message": "文件上传过于频繁，每分钟最多允许5次上传，请等待45秒后重试",
  "full_response": "",
  "response_length": 0,
  "bot_name": "GPT-3.5-Turbo",
  "attachments_processed": 0,
  "attachment_info": []
}
```

### 2. 限流状态查询接口 (新增)
```
POST /rate-limit/status
```

**请求参数:**
```json
{
  "api_key": "your_poe_api_key_here"
}
```

**响应示例:**
```json
{
  "limit": 5,
  "remaining": 3,
  "used": 2,
  "reset_in_seconds": 45,
  "window_seconds": 60
}
```

**字段说明:**
- `limit`: 每分钟限制次数
- `remaining`: 剩余可用次数
- `used`: 已使用次数
- `reset_in_seconds`: 重置倒计时（秒）
- `window_seconds`: 时间窗口（秒）

## 使用示例

### Python 示例

```python
import requests
import time

def check_rate_limit(api_key):
    """查询限流状态"""
    response = requests.post("http://localhost:8081/rate-limit/status", 
                           json={"api_key": api_key})
    if response.status_code == 200:
        return response.json()
    return None

def upload_file_with_rate_limit_check(api_key, file_urls, content):
    """带限流检查的文件上传"""
    
    # 先检查限流状态
    rate_status = check_rate_limit(api_key)
    if rate_status and rate_status['remaining'] == 0:
        print(f"限流中，请等待 {rate_status['reset_in_seconds']} 秒")
        return None
    
    # 执行文件上传
    payload = {
        "role": "user",
        "content": content,
        "bot_name": "GPT-3.5-Turbo",
        "api_key": api_key,
        "file_urls": file_urls
    }
    
    response = requests.post("http://localhost:8081/chat/with-files", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        if not data['success'] and "文件上传过于频繁" in data['message']:
            print(f"触发限流: {data['message']}")
        return data
    
    return None

# 使用示例
api_key = "your_api_key_here"
file_urls = ["https://example.com/document.pdf"]

result = upload_file_with_rate_limit_check(api_key, file_urls, "请分析这个文件")
if result:
    print(f"上传结果: {result['success']}")
```

## 配置说明

限流参数在 `app/utils/rate_limiter.py` 中定义：

```python
# 默认配置
limit: int = 5        # 每分钟限制次数
window: int = 60      # 时间窗口（秒）
```

如需修改限流参数，可以在调用时传入不同的值。

## 测试

运行测试脚本验证限流功能：

```bash
# 修改 test_rate_limit.py 中的 API_KEY
python test_rate_limit.py
```

## 注意事项

1. **API Key 安全**: 限流基于 API Key，请妥善保管
2. **内存存储**: 当前使用内存存储限流状态，服务重启后重置
3. **单实例**: 适用于单实例部署，多实例需要使用 Redis
4. **精确计数**: 只有成功调用 `fp.upload_file` 才计入限流
5. **历史消息**: 历史消息中的附件上传也会计入限流

## 扩展建议

如需支持多实例部署，建议：
1. 使用 Redis 作为限流状态存储
2. 使用 `slowapi` 库简化实现
3. 考虑更复杂的限流策略（如令牌桶算法）
