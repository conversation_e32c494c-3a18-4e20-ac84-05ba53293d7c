"""
文件上传限流器
"""

import asyncio
from collections import defaultdict
from time import time
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)


class FileUploadRateLimiter:
    """
    文件上传专用限流器
    基于API key进行限流，只有实际调用文件上传时才计数
    """
    
    def __init__(self):
        self.upload_requests: Dict[str, List[float]] = defaultdict(list)
        self.lock = asyncio.Lock()
    
    async def check_upload_rate_limit(
        self, 
        api_key: str, 
        limit: int = 5, 
        window: int = 60
    ) -> Tuple[bool, str]:
        """
        检查文件上传限流
        
        Args:
            api_key: API密钥，用作限流标识
            limit: 限制次数，默认5次
            window: 时间窗口，默认60秒
            
        Returns:
            (是否允许, 错误信息)
        """
        async with self.lock:
            now = time()
            window_start = now - window
            
            # 清理过期的请求记录
            self.upload_requests[api_key] = [
                req_time for req_time in self.upload_requests[api_key] 
                if req_time > window_start
            ]
            
            current_count = len(self.upload_requests[api_key])
            
            if current_count >= limit:
                remaining_time = int(self.upload_requests[api_key][0] + window - now)
                return False, f"文件上传过于频繁，每分钟最多允许{limit}次上传，请等待{remaining_time}秒后重试"
            
            return True, ""
    
    async def record_upload_attempt(self, api_key: str) -> None:
        """
        记录一次文件上传尝试
        
        Args:
            api_key: API密钥
        """
        async with self.lock:
            now = time()
            self.upload_requests[api_key].append(now)
            logger.info(f"记录文件上传尝试，API key: {api_key[:10]}..., 当前计数: {len(self.upload_requests[api_key])}")
    
    async def get_remaining_quota(self, api_key: str, limit: int = 5, window: int = 60) -> Dict[str, int]:
        """
        获取剩余配额信息
        
        Args:
            api_key: API密钥
            limit: 限制次数
            window: 时间窗口
            
        Returns:
            包含剩余配额信息的字典
        """
        async with self.lock:
            now = time()
            window_start = now - window
            
            # 清理过期记录
            self.upload_requests[api_key] = [
                req_time for req_time in self.upload_requests[api_key] 
                if req_time > window_start
            ]
            
            current_count = len(self.upload_requests[api_key])
            remaining = max(0, limit - current_count)
            
            # 计算重置时间
            reset_time = 0
            if self.upload_requests[api_key]:
                oldest_request = min(self.upload_requests[api_key])
                reset_time = int(oldest_request + window - now)
            
            return {
                "limit": limit,
                "remaining": remaining,
                "used": current_count,
                "reset_in_seconds": max(0, reset_time),
                "window_seconds": window
            }


# 全局限流器实例
file_upload_limiter = FileUploadRateLimiter()
