# System Prompt 功能实现文档

## 🎯 功能概述

为 `/chat` 接口新增了 `system_prompt` 参数，用于设置AI的角色和行为模式，让用户可以自定义对话背景。

## ✅ 已实现功能

### 1. 数据模型更新

#### ChatRequest模型
```python
class ChatRequest(BaseModel):
    role: str = Field("user", description="角色 (user, bot, system)")
    content: str = Field(..., description="消息内容")
    bot_name: str = Field("GPT-3.5-Turbo", description="机器人名称")
    api_key: str = Field(..., description="Poe API 密钥")
    system_prompt: Optional[str] = Field(None, description="系统提示词，用于设置对话背景")
```

### 2. 服务层实现

#### 核心逻辑
```python
# 构建消息列表
messages = []

# 如果有系统提示词，先添加系统消息
if system_prompt:
    messages.append(fp.ProtocolMessage(
        role="system",
        content=system_prompt
    ))

# 添加用户消息
messages.append(fp.ProtocolMessage(role=role, content=content))

# 调用Poe API
async for partial in fp.get_bot_response(
    messages=messages,
    bot_name=bot_name,
    api_key=api_key
):
    # 处理响应...
```

### 3. API接口

#### 请求格式
```json
POST /chat
{
  "role": "user",
  "content": "你好，请介绍一下你自己",
  "bot_name": "GPT-3.5-Turbo",
  "api_key": "your_poe_api_key_here",
  "system_prompt": "你是一个友善的AI助手，请用简洁明了的方式回答问题。"
}
```

#### 响应格式
```json
{
  "success": true,
  "message": "请求成功",
  "full_response": "你好！我是一个AI助手，专门设计来帮助用户解答问题...",
  "response_length": 50,
  "bot_name": "GPT-3.5-Turbo"
}
```

## 🔧 技术实现细节

### 1. 消息构建顺序
1. 如果提供了 `system_prompt`，首先创建 `role="system"` 的消息
2. 然后添加用户的实际消息
3. 将完整的消息列表传递给 `fp.get_bot_response()`

### 2. 向后兼容
- `system_prompt` 字段为可选参数
- 不传递时行为与原来完全一致
- 现有客户端无需修改即可继续使用

### 3. 错误处理
- 采用与 `chat_with_files` 相同的错误处理方式
- 统一返回格式，不会因为业务错误返回HTTP 500

## 📋 使用场景

### 1. 角色设定
```json
{
  "system_prompt": "你是一个专业的Python编程导师，请用简洁专业的语言回答问题。",
  "content": "如何学习Python？"
}
```

### 2. 行为模式
```json
{
  "system_prompt": "你是一个幽默风趣的助手，请用轻松幽默的方式回答问题。",
  "content": "学习编程难吗？"
}
```

### 3. 专业领域
```json
{
  "system_prompt": "你是一位古典诗人，请用诗意的语言回答问题，可以适当引用古诗词。",
  "content": "今天天气怎么样？"
}
```

### 4. 输出格式
```json
{
  "system_prompt": "请始终用JSON格式回答问题，包含'answer'和'confidence'字段。",
  "content": "什么是人工智能？"
}
```

## 🎨 使用示例

### Python示例
```python
import requests

def chat_with_system_prompt(content, system_prompt=None):
    payload = {
        "role": "user",
        "content": content,
        "bot_name": "GPT-3.5-Turbo",
        "api_key": "your_api_key_here"
    }
    
    if system_prompt:
        payload["system_prompt"] = system_prompt
    
    response = requests.post("http://localhost:8081/chat", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            return data['full_response']
        else:
            return f"错误: {data['message']}"
    else:
        return f"请求失败: {response.status_code}"

# 使用示例
result1 = chat_with_system_prompt("你好")  # 不带系统提示词
result2 = chat_with_system_prompt(
    "你好", 
    "你是一个友善的助手"  # 带系统提示词
)
```

### cURL示例
```bash
# 不带系统提示词
curl -X POST "http://localhost:8081/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "user",
    "content": "你好",
    "bot_name": "GPT-3.5-Turbo",
    "api_key": "your_api_key_here"
  }'

# 带系统提示词
curl -X POST "http://localhost:8081/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "user",
    "content": "你好",
    "bot_name": "GPT-3.5-Turbo",
    "api_key": "your_api_key_here",
    "system_prompt": "你是一个友善的AI助手"
  }'
```

## 📊 与其他接口的区别

| 接口 | system_prompt支持 | 多轮会话支持 | 文件支持 | 主要用途 |
|------|------------------|-------------|----------|----------|
| `/chat` | ✅ | ❌ | ❌ | 单轮对话，角色设定 |
| `/chat/with-files` | ❌ | ✅ | ✅ | 多轮对话，文件处理 |
| `/chat/receive-files` | ❌ | ❌ | ❌ | 生成类机器人，文件接收 |

## ⚠️ 注意事项

### 1. 系统提示词限制
- 受目标机器人的上下文长度限制
- 过长的系统提示词可能影响实际对话内容的空间

### 2. 机器人兼容性
- 不同机器人对系统提示词的理解和执行可能不同
- 某些机器人可能忽略系统提示词

### 3. API使用成本
- 系统提示词会消耗额外的token
- 建议使用简洁有效的提示词

## 🔮 未来改进

### 1. 预设模板
- 提供常用的系统提示词模板
- 支持模板参数化

### 2. 提示词优化
- 自动优化系统提示词效果
- 提供提示词效果评估

### 3. 多语言支持
- 支持不同语言的系统提示词
- 自动翻译功能

## 📚 相关文档

- [README.md](./README.md) - 项目总体说明
- [Poe API Reference](https://creator.poe.com/docs/fastapi_poe-python-reference)

## 🎉 总结

`system_prompt` 功能已成功实现，为 `/chat` 接口提供了强大的角色设定能力。用户可以通过系统提示词来：

- 🎭 设定AI的角色和性格
- 📝 指定回答的格式和风格  
- 🎯 限定回答的领域和范围
- 🔧 控制AI的行为模式

该功能完全向后兼容，不影响现有的使用方式，为用户提供了更灵活的对话控制能力。
