[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "poe-connection"
version = "1.0.0"
description = "FastAPI based Poe API service for AI model connections"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
keywords = ["poe", "api", "fastapi", "ai", "chatbot"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "fastapi==0.116.1",
    "uvicorn[standard]==0.24.0",
    "fastapi-poe",
    "pydantic-settings==2.5.2",
    "python-dotenv==1.0.0",
    "requests==2.31.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.991",
]

[project.scripts]
poe-connection = "poe_connection.run:main"

[project.urls]
Homepage = "https://github.com/yourusername/poe-connection"
"Bug Reports" = "https://github.com/yourusername/poe-connection/issues"
"Source" = "https://github.com/yourusername/poe-connection"
Documentation = "https://github.com/yourusername/poe-connection/blob/main/README.md"

[tool.setuptools]
packages = ["app", "app.models", "app.routers", "app.services", "poe_connection"]
include-package-data = true

[tool.setuptools.package-data]
app = ["*.py"]
poe_connection = ["*.py"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
