"""
FastAPI 应用主入口
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.config import settings
from app.routers import chat


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动事件
    print(f"🚀 {settings.app_name} v{settings.app_version} 启动中...")
    print(f"📡 服务地址: http://{settings.server_host}:{settings.server_port}")
    print(f"🔗 代理状态: {'启用' if settings.use_proxy else '禁用'}")
    if settings.use_proxy:
        print(f"🌐 代理地址: {settings.proxy_url}")
    print(f"🤖 API Key: 通过请求参数传递")
    print(f"📖 API 文档: http://localhost:{settings.server_port}/docs")

    yield

    # 关闭事件
    print("👋 应用正在关闭...")


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    description="基于 fastapi_poe 的 Poe API Web 服务",
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加 CORS 中间件 - 允许所有来源
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有域名
    allow_credentials=False,  # 当 allow_origins=["*"] 时，必须设置为 False
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有请求头
)

# 包含路由
app.include_router(chat.router, tags=["聊天"])


if __name__ == "__main__":
    print(f"🚀 启动 {settings.app_name}...")
    print(f"📡 端口: {settings.server_port}")
    print(f"🔗 代理: {'启用' if settings.use_proxy else '禁用'}")
    print(f"🤖 API Key: {settings.poe_api_key[:10]}...")
    print(f"📖 访问 http://localhost:{settings.server_port}/docs 查看 API 文档")
    
    uvicorn.run(
        "app.main:app",
        host=settings.server_host,
        port=settings.server_port,
        log_level=settings.log_level,
        reload=True  # 开发模式下启用热重载
    )
