"""
聊天相关的数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from fastapi import UploadFile


class ChatRequest(BaseModel):
    """聊天请求模型"""
    role: str = Field("user", description="角色 (user, bot, system)")
    content: str = Field(..., description="消息内容")
    bot_name: str = Field("GPT-3.5-Turbo", description="机器人名称")
    api_key: str = Field(..., description="Poe API 密钥")
    system_prompt: Optional[str] = Field(None, description="系统提示词，用于设置对话背景")

    class Config:
        json_schema_extra = {
            "example": {
                "role": "user",
                "content": "你好，请介绍一下你自己",
                "bot_name": "GPT-3.5-Turbo",
                "api_key": "your_poe_api_key_here",
                "system_prompt": "你是一个友善的AI助手，请用简洁明了的方式回答问题。"
            }
        }


class ChatResponse(BaseModel):
    """聊天响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="状态消息")
    full_response: str = Field(..., description="完整响应内容")
    think_response: str = Field("", description="思考过程内容")
    answer_response: str = Field("", description="答案内容")
    response_length: int = Field(..., description="响应长度")
    bot_name: str = Field(..., description="使用的机器人名称")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "请求成功",
                "full_response": "你好！我是ChatGPT，一个AI助手...",
                "think_response": "",
                "answer_response": "你好！我是ChatGPT，一个AI助手...",
                "response_length": 50,
                "bot_name": "GPT-3.5-Turbo"
            }
        }


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    proxy_enabled: bool = Field(..., description="代理是否启用")
    api_key_configured: bool = Field(..., description="API Key 是否配置")


class ServiceInfoResponse(BaseModel):
    """服务信息响应模型"""
    service: str = Field(..., description="服务名称")
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="服务版本")
    proxy_enabled: bool = Field(..., description="代理是否启用")
    proxy_url: Optional[str] = Field(None, description="代理URL")
    endpoints: dict = Field(..., description="可用端点")


class AttachmentInfo(BaseModel):
    """附件信息模型"""
    url: str = Field(..., description="附件URL")
    content_type: str = Field(..., description="附件MIME类型")
    name: str = Field(..., description="附件名称")
    parsed_content: Optional[str] = Field(None, description="解析后的内容")


class MessageHistory(BaseModel):
    """对话历史消息模型"""
    role: str = Field(..., description="角色 (user, bot, system)")
    content: str = Field(..., description="消息内容")
    attachments: Optional[List[str]] = Field(None, description="附件URL列表")


class ChatWithFilesRequest(BaseModel):
    """支持文件的聊天请求模型"""
    role: str = Field("user", description="角色 (user, bot, system)")
    content: str = Field(..., description="消息内容")
    bot_name: str = Field("GPT-3.5-Turbo", description="机器人名称")
    api_key: str = Field(..., description="Poe API 密钥")
    file_urls: Optional[List[str]] = Field(None, description="文件URL列表")
    conversation_history: Optional[List[MessageHistory]] = Field(None, description="对话历史")
    system_prompt: Optional[str] = Field(None, description="系统提示词，用于设置对话背景")

    class Config:
        json_schema_extra = {
            "example": {
                "role": "user",
                "content": "请分析这个文件的内容",
                "bot_name": "GPT-3.5-Turbo",
                "api_key": "your_poe_api_key_here",
                "file_urls": ["https://example.com/file1.pdf", "https://example.com/file2.txt"],
                "conversation_history": [
                    {
                        "role": "user",
                        "content": "你好"
                    },
                    {
                        "role": "bot",
                        "content": "你好！我是AI助手，有什么可以帮助你的吗？"
                    }
                ],
                "system_prompt": "你是一个专业的文档分析师，请用简洁专业的语言分析文件内容。"
            }
        }


class ChatWithFilesResponse(BaseModel):
    """支持文件的聊天响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="状态消息")
    full_response: str = Field(..., description="完整响应内容")
    think_response: str = Field("", description="思考过程内容")
    answer_response: str = Field("", description="答案内容")
    response_length: int = Field(..., description="响应长度")
    bot_name: str = Field(..., description="使用的机器人名称")
    attachments_processed: int = Field(0, description="处理的附件数量")
    attachment_info: Optional[List[AttachmentInfo]] = Field(None, description="附件信息列表")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "请求成功",
                "full_response": "根据您提供的文件，我分析了以下内容...",
                "think_response": "",
                "answer_response": "根据您提供的文件，我分析了以下内容...",
                "response_length": 150,
                "bot_name": "GPT-3.5-Turbo",
                "attachments_processed": 2,
                "attachment_info": [
                    {
                        "url": "https://example.com/file1.pdf",
                        "content_type": "application/pdf",
                        "name": "file1.pdf",
                        "parsed_content": "文件内容摘要..."
                    }
                ]
            }
        }


class ChatReceiveFilesRequest(BaseModel):
    """接收文件的聊天请求模型"""
    role: str = Field("user", description="角色 (user, bot, system)")
    content: str = Field(..., description="消息内容")
    bot_name: str = Field("Imagen-4-Ultra-Exp", description="生成类机器人名称")
    api_key: str = Field(..., description="Poe API 密钥")

    class Config:
        json_schema_extra = {
            "example": {
                "role": "user",
                "content": "生成一张美丽的风景图片",
                "bot_name": "Imagen-4-Ultra-Exp",
                "api_key": "your_poe_api_key_here"
            }
        }


class ReceivedAttachment(BaseModel):
    """接收到的附件信息模型"""
    url: str = Field(..., description="附件URL")
    content_type: str = Field(..., description="附件MIME类型")
    name: str = Field(..., description="附件名称")
    size: Optional[int] = Field(None, description="文件大小（字节）")


class RateLimitRequest(BaseModel):
    """限流状态查询请求模型"""
    api_key: str = Field(..., description="Poe API 密钥")

    class Config:
        json_schema_extra = {
            "example": {
                "api_key": "your_poe_api_key_here"
            }
        }


class RateLimitResponse(BaseModel):
    """限流状态响应模型"""
    limit: int = Field(..., description="每分钟限制次数")
    remaining: int = Field(..., description="剩余可用次数")
    used: int = Field(..., description="已使用次数")
    reset_in_seconds: int = Field(..., description="重置倒计时（秒）")
    window_seconds: int = Field(..., description="时间窗口（秒）")

    class Config:
        json_schema_extra = {
            "example": {
                "limit": 5,
                "remaining": 3,
                "used": 2,
                "reset_in_seconds": 45,
                "window_seconds": 60
            }
        }


class ChatReceiveFilesResponse(BaseModel):
    """接收文件的聊天响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="状态消息")
    full_response: str = Field(..., description="完整文本响应内容")
    think_response: str = Field("", description="思考过程内容")
    answer_response: str = Field("", description="答案内容")
    response_length: int = Field(..., description="文本响应长度")
    bot_name: str = Field(..., description="使用的机器人名称")
    received_files: List[ReceivedAttachment] = Field(default_factory=list, description="接收到的文件列表")
    files_count: int = Field(0, description="接收到的文件数量")

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "请求成功",
                "full_response": "我已经为您生成了一张美丽的风景图片。",
                "think_response": "",
                "answer_response": "我已经为您生成了一张美丽的风景图片。",
                "response_length": 20,
                "bot_name": "Imagen-4-Ultra-Exp",
                "received_files": [
                    {
                        "url": "https://example.com/generated_image.png",
                        "content_type": "image/png",
                        "name": "generated_image.png",
                        "size": 1024000
                    }
                ],
                "files_count": 1
            }
        }
