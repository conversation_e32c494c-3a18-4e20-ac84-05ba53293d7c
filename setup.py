#!/usr/bin/env python3
"""
Poe Connection 项目安装配置
"""

from setuptools import setup, find_packages
import os

# 读取 README 文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Poe Connection - FastAPI based Poe API service"

# 读取依赖
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="poe-connection",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="FastAPI based Poe API service for AI model connections",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/poe-connection",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Framework :: FastAPI",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.991",
        ],
    },
    entry_points={
        "console_scripts": [
            "poe-connection=run:main",
        ],
    },
    include_package_data=True,
    package_data={
        "app": ["*.py"],
    },
    zip_safe=False,
    keywords="poe api fastapi ai chatbot",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/poe-connection/issues",
        "Source": "https://github.com/yourusername/poe-connection",
        "Documentation": "https://github.com/yourusername/poe-connection/blob/main/README.md",
    },
)
