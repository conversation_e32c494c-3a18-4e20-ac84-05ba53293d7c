# Poe Connection 打包和使用指南

## 1. 项目打包成 Wheel

### 1.1 安装打包工具

首先确保安装了必要的打包工具：

```powershell
pip install --upgrade pip setuptools wheel build
```

### 1.2 清理之前的构建文件

```powershell
# 删除之前的构建文件
Remove-Item -Recurse -Force build, dist, *.egg-info -ErrorAction SilentlyContinue
```

### 1.3 构建 Wheel 包

使用现代的 `build` 工具（推荐）：

```powershell
python -m build
```

或者使用传统的 `setup.py` 方式：

```powershell
python setup.py sdist bdist_wheel
```

构建完成后，在 `dist/` 目录下会生成：
- `poe-connection-1.0.0.tar.gz` (源码分发包)
- `poe_connection-1.0.0-py3-none-any.whl` (wheel 包)

### 1.4 验证构建的包

```powershell
# 检查包内容
python -m zipfile -l dist/poe_connection-1.0.0-py3-none-any.whl

# 验证包的元数据
pip install twine
twine check dist/*
```

## 2. 安装和使用 Wheel 包

### 2.1 本地安装

```powershell
# 直接安装 wheel 包
pip install dist/poe_connection-1.0.0-py3-none-any.whl
pip install poe_connection-1.0.0-py3-none-any.whl

# 或者安装开发版本（包含开发依赖）
pip install "dist/poe_connection-1.0.0-py3-none-any.whl[dev]"
```

### 2.2 从其他位置安装

```powershell
# 如果 wheel 文件在其他位置
pip install /path/to/poe_connection-1.0.0-py3-none-any.whl

# 通过 URL 安装（如果上传到了文件服务器）
pip install https://example.com/path/to/poe_connection-1.0.0-py3-none-any.whl
```

### 2.3 使用安装的包

安装后，可以通过以下方式使用：

#### 方式1：命令行启动

```powershell
# 查看帮助信息
poe-connection --help

# 启动服务（默认配置）
poe-connection

# 自定义主机和端口
poe-connection --host 0.0.0.0 --port 8000

# 开发模式（启用热重载和调试日志）
poe-connection --dev

# 生产模式（多进程，禁用热重载）
poe-connection --prod --workers 4

# 自定义日志级别
poe-connection --log-level debug

# 启用热重载
poe-connection --reload
```

#### 方式2：Python 代码中使用

```python
# 导入应用模块
from app.main import app
from app.config import settings

# 或者直接运行主函数
from run import main
main()
```

#### 方式3：作为模块运行

```powershell
# 运行主模块
python -m run --help
```

## 3. 卸载包

```powershell
pip uninstall poe-connection
```

## 4. 发布到 PyPI（可选）

### 4.1 配置 PyPI 凭据

```powershell
# 安装 twine
pip install twine

# 配置 PyPI 凭据（首次）
# 创建 ~/.pypirc 文件或使用环境变量
```

### 4.2 上传到 PyPI

```powershell
# 上传到测试 PyPI
twine upload --repository testpypi dist/*

# 上传到正式 PyPI
twine upload dist/*
```

### 4.3 从 PyPI 安装

```powershell
# 从测试 PyPI 安装
pip install --index-url https://test.pypi.org/simple/ poe-connection

# 从正式 PyPI 安装
pip install poe-connection
```

## 5. 开发模式安装

如果你想在开发时安装包（可编辑模式）：

```powershell
# 可编辑安装，代码修改会立即生效
pip install -e .

# 包含开发依赖的可编辑安装
pip install -e ".[dev]"
```

## 6. 常见问题

### 6.1 构建失败

- 确保所有依赖都在 `requirements.txt` 中
- 检查 `setup.py` 或 `pyproject.toml` 配置
- 确保 Python 版本兼容

### 6.2 安装失败

- 检查 Python 版本是否满足要求（>=3.8）
- 确保所有依赖都可以安装
- 检查网络连接

### 6.3 命令行工具不可用

- 确保安装时没有错误
- 检查 PATH 环境变量
- 重新激活虚拟环境（如果使用）

## 7. 自动化脚本

### 7.1 构建脚本 (build.ps1)

```powershell
# 清理
Remove-Item -Recurse -Force build, dist, *.egg-info -ErrorAction SilentlyContinue

# 构建
python -m build

# 验证
twine check dist/*

Write-Host "构建完成！文件位于 dist/ 目录"
```

### 7.2 安装脚本 (install.ps1)

```powershell
# 查找最新的 wheel 文件
$wheelFile = Get-ChildItem -Path "dist" -Filter "*.whl" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($wheelFile) {
    Write-Host "安装 $($wheelFile.Name)..."
    pip install $wheelFile.FullName --force-reinstall
    Write-Host "安装完成！"
} else {
    Write-Host "未找到 wheel 文件，请先运行构建"
}
```
