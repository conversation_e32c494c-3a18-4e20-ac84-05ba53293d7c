"""
Poe API 服务逻辑
"""

import fastapi_poe as fp
from typing import Dict, Any, List, Optional
from app.config import settings
from app.utils.rate_limiter import file_upload_limiter
import time

class PoeService:
    """Poe API 服务类"""

    def __init__(self):
        # 不再从配置中读取默认 API Key
        pass

    def _parse_thinking_response(self, full_response: str) -> tuple[str, str]:
        """
        解析包含思考过程的响应内容

        Args:
            full_response: 完整的响应内容

        Returns:
            tuple: (think_response, answer_response)
        """
        if not full_response:
            return "", ""

        import re

        # 更精确的匹配：从 *Thinking...* 开始，到遇到中文内容"一. 标题"为止
        thinking_pattern = r'\*Thinking\.\.\.\*\n\n>.*?(?=一\.)'

        # 找到所有thinking块
        thinking_matches = list(re.finditer(thinking_pattern, full_response, re.DOTALL))

        if not thinking_matches:
            # 没有找到thinking块，全部作为答案
            return "", full_response.strip()

        # 提取thinking内容
        think_parts = []
        for match in thinking_matches:
            thinking_content = match.group(0)
            # 清理thinking内容：去掉开头的 *Thinking...*\n\n>
            clean_thinking = re.sub(r'^\*Thinking\.\.\.\*\n\n>', '', thinking_content)
            # 去掉结尾可能的空白和 > 符号
            clean_thinking = re.sub(r'>\s*$', '', clean_thinking)
            clean_thinking = clean_thinking.strip()
            if clean_thinking:
                think_parts.append(clean_thinking)

        # 移除所有thinking块，剩下的就是答案内容
        answer_content = full_response
        for match in reversed(thinking_matches):  # 从后往前删除，避免位置偏移
            answer_content = answer_content[:match.start()] + answer_content[match.end():]

        # 清理答案内容中可能的重复
        # 如果答案内容中有重复的"一. 标题"，只保留第一个完整的部分
        if "一. 标题" in answer_content:
            # 找到第一个"一. 标题"的位置
            first_title_pos = answer_content.find("一. 标题")
            if first_title_pos != -1:
                # 从第一个"一. 标题"开始，找到下一个"一. 标题"的位置
                remaining_content = answer_content[first_title_pos:]
                second_title_pos = remaining_content.find("一. 标题", 1)  # 从位置1开始查找

                if second_title_pos != -1:
                    # 如果找到第二个"一. 标题"，只保留到第二个之前的内容
                    answer_content = answer_content[:first_title_pos + second_title_pos]

        think_response = "\n\n".join(think_parts) if think_parts else ""
        answer_response = answer_content.strip() if answer_content.strip() else ""

        return think_response, answer_response

    async def chat(self, role: str, content: str, bot_name: str, api_key: str, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        发送聊天请求到 Poe API

        Args:
            role: 角色 (user, bot, system)
            content: 消息内容
            bot_name: 机器人名称
            api_key: Poe API 密钥
            system_prompt: 系统提示词，用于设置对话背景

        Returns:
            包含响应结果的字典
        """
        try:
            # 构建消息列表
            messages = []

            # 如果有系统提示词，先添加系统消息
            if system_prompt:
                messages.append(fp.ProtocolMessage(
                    role="system",
                    content=system_prompt
                ))

            # 添加用户消息
            messages.append(fp.ProtocolMessage(role=role, content=content))
            
            full_response = ""
            
            try:
                async for partial in fp.get_bot_response(
                    messages=messages,
                    bot_name=bot_name,
                    api_key=api_key
                ):
                    if hasattr(partial, 'text') and partial.text:
                        full_response += partial.text
            
            except Exception as e:
                return {
                    "success": False,
                    "message": f"流式响应错误: {str(e)}",
                    "full_response": full_response,
                    "response_length": len(full_response),
                    "bot_name": bot_name
                }
            
            # 解析思考内容和答案内容
            think_response, answer_response = self._parse_thinking_response(full_response)

            return {
                "success": True,
                "message": "请求成功",
                "full_response": full_response,
                "think_response": think_response,
                "answer_response": answer_response,
                "response_length": len(full_response),
                "bot_name": bot_name
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"请求失败: {str(e)}",
                "full_response": "",
                "response_length": 0,
                "bot_name": bot_name
            }

    async def chat_with_files(self, role: str, content: str, bot_name: str, api_key: str, file_urls: Optional[List[str]] = None, conversation_history: Optional[List[Dict[str, Any]]] = None, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        发送带文件的聊天请求到 Poe API

        Args:
            role: 角色 (user, bot, system)
            content: 消息内容
            bot_name: 机器人名称
            api_key: Poe API 密钥
            file_urls: 文件URL列表
            conversation_history: 对话历史
            system_prompt: 系统提示词，用于设置对话背景

        Returns:
            包含响应结果的字典
        """
        try:
            attachments = []
            attachment_info = []

            # 只有在提供了file_urls时才处理文件上传
            if file_urls and len(file_urls) > 0:
                for file_url in file_urls:
                    try:
                        # 检查文件上传限流
                        allowed, error_msg = await file_upload_limiter.check_upload_rate_limit(api_key)
                        if not allowed:
                            return {
                                "success": False,
                                "message": error_msg,
                                "full_response": "",
                                "response_length": 0,
                                "bot_name": bot_name,
                                "attachments_processed": 0,
                                "attachment_info": []
                            }

                        # 记录文件上传尝试
                        await file_upload_limiter.record_upload_attempt(api_key)

                        # 使用Poe API上传文件
                        attachment = await fp.upload_file(
                            file_url=file_url,
                            api_key=api_key
                        )
                        print(f'文件上传{attachment}')
                        attachments.append(attachment)

                        # 记录附件信息
                        attachment_info.append({
                            "url": attachment.url,
                            "content_type": attachment.content_type,
                            "name": attachment.name,
                            "parsed_content": getattr(attachment, 'parsed_content', None)
                        })

                    except Exception as e:
                        print(f"文件上传失败 {file_url}: {str(e)}")
                        return {
                            "success": False,
                            "message": f"文件上传失败 {file_url}: {str(e)}",
                            "full_response": "",
                            "response_length": 0,
                            "bot_name": bot_name,
                            "attachments_processed": 0,
                            "attachment_info": []
                        }

                time.sleep(5)
                print(f"文件上传完成等待 5 s")
            # 构建消息列表，包含对话历史
            messages = []

            # 如果有系统提示词，先添加系统消息
            if system_prompt:
                messages.append(fp.ProtocolMessage(
                    role="system",
                    content=system_prompt
                ))

            # 添加对话历史
            if conversation_history:
                for hist_msg in conversation_history:
                    # 处理历史消息中的附件
                    hist_attachments = []
                    if hist_msg.get('attachments') and len(hist_msg['attachments']) > 0:
                        for attachment_url in hist_msg['attachments']:
                            try:
                                # 检查文件上传限流
                                allowed, error_msg = await file_upload_limiter.check_upload_rate_limit(api_key)
                                if not allowed:
                                    return {
                                        "success": False,
                                        "message": error_msg,
                                        "full_response": "",
                                        "response_length": 0,
                                        "bot_name": bot_name,
                                        "attachments_processed": 0,
                                        "attachment_info": []
                                    }

                                # 记录文件上传尝试
                                await file_upload_limiter.record_upload_attempt(api_key)

                                attachment = await fp.upload_file(
                                    file_url=attachment_url,
                                    api_key=api_key
                                )
                                hist_attachments.append(attachment)
                            except Exception as e:
                                print(f"历史消息附件上传失败 {attachment_url}: {str(e)}")
                                return {
                                    "success": False,
                                    "message": f"历史消息附件上传失败 {attachment_url}: {str(e)}",
                                    "full_response": "",
                                    "response_length": 0,
                                    "bot_name": bot_name,
                                    "attachments_processed": 0,
                                    "attachment_info": []
                                }

                    # 只有在有附件时才包含attachments字段
                    if hist_attachments:
                        messages.append(fp.ProtocolMessage(
                            role=hist_msg['role'],
                            content=hist_msg['content'],
                            attachments=hist_attachments
                        ))
                    else:
                        messages.append(fp.ProtocolMessage(
                            role=hist_msg['role'],
                            content=hist_msg['content']
                        ))

            # 添加当前消息，只有在有附件时才包含附件
            if attachments:
                current_message = fp.ProtocolMessage(
                    role=role,
                    content=content,
                    attachments=attachments
                )
            else:
                current_message = fp.ProtocolMessage(
                    role=role,
                    content=content
                )
            messages.append(current_message)
            print(messages)
            full_response = ""

            try:
                async for partial in fp.get_bot_response(
                    messages=messages,
                    bot_name=bot_name,
                    api_key=api_key
                ):
                    if hasattr(partial, 'text') and partial.text:
                        full_response += partial.text

            except Exception as e:
                return {
                    "success": False,
                    "message": f"流式响应错误: {str(e)}",
                    "full_response": full_response,
                    "response_length": len(full_response),
                    "bot_name": bot_name,
                    "attachments_processed": len(attachments),
                    "attachment_info": attachment_info
                }

            # 解析思考内容和答案内容
            think_response, answer_response = self._parse_thinking_response(full_response)

            return {
                "success": True,
                "message": "请求成功",
                "full_response": full_response,
                "think_response": think_response,
                "answer_response": answer_response,
                "response_length": len(full_response),
                "bot_name": bot_name,
                "attachments_processed": len(attachments),
                "attachment_info": attachment_info
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"请求失败: {str(e)}",
                "full_response": "",
                "response_length": 0,
                "bot_name": bot_name,
                "attachments_processed": 0,
                "attachment_info": []
            }

    async def chat_receive_files(self, role: str, content: str, bot_name: str, api_key: str) -> Dict[str, Any]:
        """
        调用生成类机器人并接收其生成的文件

        Args:
            role: 角色 (user, bot, system)
            content: 消息内容
            bot_name: 生成类机器人名称（如Imagen-4-Ultra-Exp, Imagen-3等）
            api_key: Poe API 密钥

        Returns:
            包含响应结果和接收文件的字典
        """
        try:
            # 创建单个消息
            message = fp.ProtocolMessage(role=role, content=content)

            full_response = ""
            received_files = []

            try:
                async for partial in fp.get_bot_response(
                    messages=[message],
                    bot_name=bot_name,
                    api_key=api_key
                ):
                    # 收集文本响应
                    if hasattr(partial, 'text') and partial.text:
                        full_response += partial.text

                    # 收集文件附件 - 检查attachment字段
                    if hasattr(partial, 'attachment') and partial.attachment:
                        attachment_info = {
                            "url": partial.attachment.url,
                            "content_type": partial.attachment.content_type,
                            "name": partial.attachment.name,
                            "size": getattr(partial.attachment, 'size', None)
                        }
                        received_files.append(attachment_info)
                        print(f"✅ 接收到文件: {attachment_info['name']} ({attachment_info['content_type']})")

                    # 检查data字段中的附件信息
                    if hasattr(partial, 'data') and partial.data and isinstance(partial.data, dict):
                        if 'attachment' in partial.data:
                            attachment_data = partial.data['attachment']
                            attachment_info = {
                                "url": attachment_data.get('url', ''),
                                "content_type": attachment_data.get('content_type', ''),
                                "name": attachment_data.get('name', ''),
                                "size": attachment_data.get('size', None)
                            }
                            received_files.append(attachment_info)
                            print(f"✅ 从data字段接收到文件: {attachment_info['name']} ({attachment_info['content_type']})")

            except Exception as e:
                error_message = str(e)
                print(f"调试 - 捕获到异常: {error_message}")

                # 检查是否是点数不足的错误
                if "You do not have enough points" in error_message:
                    return {
                        "success": False,
                        "message": "API使用点数不足，请充值后重试",
                        "full_response": full_response,
                        "response_length": len(full_response),
                        "bot_name": bot_name,
                        "received_files": received_files,
                        "files_count": len(received_files)
                    }

                # 检查是否是机器人不存在的错误
                if "does not exist" in error_message.lower() or "not found" in error_message.lower():
                    return {
                        "success": False,
                        "message": f"机器人 '{bot_name}' 不存在或不可用",
                        "full_response": full_response,
                        "response_length": len(full_response),
                        "bot_name": bot_name,
                        "received_files": received_files,
                        "files_count": len(received_files)
                    }

                return {
                    "success": False,
                    "message": f"请求处理失败: {error_message}",
                    "full_response": full_response,
                    "response_length": len(full_response),
                    "bot_name": bot_name,
                    "received_files": received_files,
                    "files_count": len(received_files)
                }

            # 解析思考内容和答案内容
            think_response, answer_response = self._parse_thinking_response(full_response)

            return {
                "success": True,
                "message": "请求成功",
                "full_response": full_response,
                "think_response": think_response,
                "answer_response": answer_response,
                "response_length": len(full_response),
                "bot_name": bot_name,
                "received_files": received_files,
                "files_count": len(received_files)
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"请求失败: {str(e)}",
                "full_response": "",
                "response_length": 0,
                "bot_name": bot_name,
                "received_files": [],
                "files_count": 0
            }


# 创建全局服务实例
poe_service = PoeService()
