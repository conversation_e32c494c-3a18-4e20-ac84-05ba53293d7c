# 文件聊天接口文档

## 概述

新增的 `/chat/with-files` 接口支持用户上传文件并与AI进行对话，基于Poe API的文件处理能力实现。

## 功能特性

- 🔗 支持通过URL上传文件
- 📄 自动解析文件内容
- 🤖 与现有聊天接口保持一致的API设计
- 📊 返回详细的文件处理信息
- ⚡ 异步处理，支持多个文件同时上传
- 🔄 智能处理：不传文件URL时不执行附件相关逻辑，提高性能

## API 接口

### POST /chat/with-files

#### 请求参数

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| role | string | 是 | 角色，支持 "user", "bot", "system" |
| content | string | 是 | 消息内容 |
| bot_name | string | 否 | 机器人名称，默认 "GPT-3.5-Turbo" |
| api_key | string | 是 | Poe API 密钥 |
| file_urls | array[string] | 否 | 文件URL列表 |

#### 请求示例

```json
{
  "role": "user",
  "content": "请分析这些文件的内容",
  "bot_name": "GPT-3.5-Turbo",
  "api_key": "your_poe_api_key_here",
  "file_urls": [
    "https://example.com/document.pdf",
    "https://example.com/data.txt"
  ]
}
```

#### 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| message | string | 状态消息 |
| full_response | string | AI的完整回复 |
| response_length | integer | 回复长度 |
| bot_name | string | 使用的机器人名称 |
| attachments_processed | integer | 成功处理的附件数量 |
| attachment_info | array | 附件详细信息列表 |

#### 响应示例

```json
{
  "success": true,
  "message": "请求成功",
  "full_response": "根据您提供的文件，我分析了以下内容...",
  "response_length": 150,
  "bot_name": "GPT-3.5-Turbo",
  "attachments_processed": 2,
  "attachment_info": [
    {
      "url": "https://example.com/document.pdf",
      "content_type": "application/pdf",
      "name": "document.pdf",
      "parsed_content": "文件内容摘要..."
    }
  ]
}
```

## 支持的文件类型

- 📄 PDF文档
- 📝 文本文件 (.txt, .md, .csv等)
- 🖼️ 图片文件 (.jpg, .png, .gif等)
- 📊 Office文档 (.docx, .xlsx等)
- 💻 代码文件 (.py, .js, .html等)

## 使用限制

1. **文件URL要求**：
   - 必须是有效的HTTP/HTTPS链接
   - 文件必须公开可访问
   - 建议文件大小不超过10MB

2. **并发限制**：
   - 单次请求最多支持10个文件
   - 文件处理采用异步方式，失败的文件会被跳过

3. **API限制**：
   - 需要有效的Poe API密钥
   - 遵循Poe API的使用限制和计费规则

## 错误处理

### 常见错误码

- `400`: 请求参数错误
  - 消息内容为空
  - API Key为空
  - 无效的角色类型
  - 无效的文件URL格式

- `500`: 服务器内部错误
  - Poe API调用失败
  - 文件上传失败
  - 网络连接问题

### 错误响应示例

```json
{
  "detail": "文件URL必须是有效的HTTP/HTTPS链接"
}
```

## 使用示例

### Python 示例

```python
import requests

def chat_with_files():
    url = "http://localhost:8081/chat/with-files"
    payload = {
        "role": "user",
        "content": "请分析这个文档的内容",
        "bot_name": "GPT-3.5-Turbo",
        "api_key": "your_api_key_here",
        "file_urls": ["https://example.com/document.pdf"]
    }
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        data = response.json()
        print(f"AI回复: {data['full_response']}")
        print(f"处理了 {data['attachments_processed']} 个文件")
    else:
        print(f"错误: {response.text}")

chat_with_files()
```

### cURL 示例

```bash
curl -X POST "http://localhost:8081/chat/with-files" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "user",
    "content": "请分析这个文件",
    "bot_name": "GPT-3.5-Turbo",
    "api_key": "your_api_key_here",
    "file_urls": ["https://example.com/document.pdf"]
  }'
```

## 测试

运行测试脚本验证接口功能：

```bash
# 测试文件聊天接口
python test_file_chat.py

# 运行示例
python example_file_chat.py
```

## 注意事项

1. **安全性**：请妥善保管API密钥，不要在客户端代码中硬编码
2. **性能**：文件处理可能需要较长时间，建议设置合适的超时时间
3. **成本**：使用Poe API会产生费用，请注意控制使用量
4. **兼容性**：接口与原有的 `/chat` 接口保持兼容，可以混合使用
